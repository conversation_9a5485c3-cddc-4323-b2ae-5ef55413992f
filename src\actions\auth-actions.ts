"use server"

// Authentication actions for Trustay API
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { apiClient } from '../lib/api-client';
import {
  LoginRequest,
  RegisterRequest,
  RegisterDirectRequest,
  AuthResponse,
  VerificationResponse,
  UserProfile,
} from '../types/types';

// Helper function to get token from cookies
const getTokenFromCookies = async (): Promise<string | null> => {
  const cookieStore = await cookies();
  return cookieStore.get('accessToken')?.value || null;
};


// Helper function to set auth cookies
async function setAuthCookies(accessToken: string, refreshToken: string) {
  const cookieStore = await cookies();

  cookieStore.set('accessToken', accessToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60 * 24 * 7, // 7 days
  });

  cookieStore.set('refreshToken', refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60 * 24 * 30, // 30 days
  });
}

// Send email verification code
export const sendEmailVerification = async (
  email: string
): Promise<VerificationResponse> => {
  return await apiClient<VerificationResponse>('/api/verification/send', {
    method: 'POST',
    data: {
      type: 'email',
      email,
    },
  });
};

// Verify email code
export const verifyEmailCode = async (
  email: string,
  code: string
): Promise<VerificationResponse> => {
  return await apiCall<VerificationResponse>('/api/verification/verify', {
    method: 'POST',
    data: {
      type: 'email',
      email,
      code,
    },
  });
};

// Register with verification
export const registerWithVerification = async (
  userData: RegisterRequest,
  verificationToken: string
): Promise<AuthResponse> => {
  const response = await apiCall<AuthResponse>('/api/auth/register', {
    method: 'POST',
    headers: {
      'X-Verification-Token': verificationToken,
    },
    data: userData,
  });

  // Store tokens in cookies
  await setAuthCookies(response.access_token, response.refresh_token);

  return response;
};

// Register direct (for development)
export const registerDirect = async (
  userData: RegisterDirectRequest
): Promise<AuthResponse> => {
  const response = await apiCall<AuthResponse>('/api/auth/register-direct', {
    method: 'POST',
    data: userData,
  });

  // Store tokens in cookies
  await setAuthCookies(response.access_token, response.refresh_token);

  return response;
};

// Login
export const login = async (credentials: LoginRequest): Promise<AuthResponse> => {
  const response = await apiCall<AuthResponse>('/api/auth/login', {
    method: 'POST',
    data: credentials,
  });

  // Store tokens in cookies
  await setAuthCookies(response.access_token, response.refresh_token);

  return response;
};

// Get current user
export const getCurrentUser = async (): Promise<UserProfile> => {
  return await apiCall<UserProfile>('/api/auth/me', {
    method: 'GET',
  });
};

// Refresh token
export const refreshToken = async (): Promise<AuthResponse> => {
  const cookieStore = await cookies();
  const refreshTokenValue = cookieStore.get('refreshToken')?.value;

  if (!refreshTokenValue) {
    throw new Error('No refresh token available');
  }

  const response = await apiCall<AuthResponse>('/api/auth/refresh', {
    method: 'POST',
    data: {
      refreshToken: refreshTokenValue,
    },
  });

  // Update stored tokens
  await setAuthCookies(response.access_token, response.refresh_token);

  return response;
};

// Update user profile
export const updateUserProfile = async (
  profileData: Record<string, unknown>
): Promise<UserProfile> => {
  return await apiCall<UserProfile>('/api/users/profile', {
    method: 'PUT',
    data: profileData,
  });
};

// Logout
export const logout = async (): Promise<void> => {
  const cookieStore = await cookies();
  cookieStore.delete('accessToken');
  cookieStore.delete('refreshToken');
};

// Complete registration and redirect
export async function completeRegistration(formData: FormData) {
  const role = formData.get('role') as string;

  if (role === 'tenant') {
    redirect('/dashboard/tenant');
  } else if (role === 'landlord') {
    redirect('/dashboard/landlord');
  } else {
    redirect('/');
  }
}

// Skip profile update and redirect to home
export async function skipProfileUpdate() {
  redirect('/');
}
